#!/usr/bin/env python3
"""
测试向运行中的系统输入"运算26加8"
"""
import time
import requests
import json

def test_web_input():
    """通过Web接口测试输入"""
    try:
        # 测试Web服务是否可用
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        print(f"Web服务状态: {response.status_code}")
        
        # 尝试发送运算请求
        data = {
            "query": "运算26加8",
            "type": "calculation"
        }
        
        # 尝试不同的端点
        endpoints = ["/api/query", "/query", "/calculate", "/input"]
        
        for endpoint in endpoints:
            try:
                response = requests.post(f"http://127.0.0.1:5000{endpoint}", 
                                       json=data, timeout=5)
                print(f"端点 {endpoint}: {response.status_code}")
                if response.status_code == 200:
                    print(f"响应: {response.text}")
                    return True
            except requests.exceptions.RequestException as e:
                print(f"端点 {endpoint} 请求失败: {e}")
                
    except Exception as e:
        print(f"Web接口测试失败: {e}")
        return False

def test_direct_input():
    """直接通过AgentStarter输入"""
    try:
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        
        print("=== 直接输入测试 ===")
        print(f"当前inputQueueStr: '{AgentStarter.inputQueueStr}'")
        
        # 设置输入队列
        AgentStarter.inputQueueStr = "运算26加8"
        print(f"设置后inputQueueStr: '{AgentStarter.inputQueueStr}'")
        
        # 如果NARS可用，尝试直接输入
        if AgentStarter.nar is not None:
            print("NARS可用，尝试直接输入...")
            
            # 输入基本知识
            knowledge_inputs = [
                "<26 --> 数字>.",
                "<8 --> 数字>.",
                "<34 --> 数字>.",
                "<(*, 26, 8) --> 加法操作>.",
                "<(*, 26, 8, 34) --> 加法结果>.",
            ]
            
            for knowledge in knowledge_inputs:
                try:
                    print(f"输入知识: {knowledge}")
                    AgentStarter.nar.add_input(knowledge)
                    time.sleep(0.1)
                except Exception as e:
                    print(f"输入知识失败: {e}")
            
            # 询问结果
            question = "<?x --> (*, 26, 加, 8, 结果)>?"
            try:
                print(f"询问: {question}")
                AgentStarter.nar.add_input(question)
            except Exception as e:
                print(f"询问失败: {e}")
                
            print("等待推理结果...")
            time.sleep(5)
            
        else:
            print("NARS不可用")
            
        return True
        
    except Exception as e:
        print(f"直接输入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== 测试向运行中的系统输入运算请求 ===")
    
    # 首先测试Web接口
    print("\n1. 测试Web接口...")
    web_success = test_web_input()
    
    # 然后测试直接输入
    print("\n2. 测试直接输入...")
    direct_success = test_direct_input()
    
    print(f"\n=== 测试结果 ===")
    print(f"Web接口: {'成功' if web_success else '失败'}")
    print(f"直接输入: {'成功' if direct_success else '失败'}")

if __name__ == "__main__":
    main()
