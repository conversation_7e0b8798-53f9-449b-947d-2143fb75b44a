#!/usr/bin/env python3
"""
向Neo4j数据库添加运算知识
"""
from py2neo import Graph
import logging

def setup_calculation_knowledge():
    """设置运算知识到Neo4j数据库"""
    try:
        # 连接Neo4j数据库
        neo4j_uri = "bolt://localhost:7687"
        neo4j_user = "neo4j"
        neo4j_password = "12345678"
        
        print(f"连接Neo4j数据库: {neo4j_uri}")
        graph_db = Graph(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        # 清除现有的运算相关数据
        print("清除现有运算数据...")
        graph_db.run("MATCH (n) WHERE n.name CONTAINS '26' OR n.name CONTAINS '加' OR n.name CONTAINS '8' OR n.name CONTAINS '34' DETACH DELETE n")
        
        # 添加基本数字节点
        print("添加基本数字节点...")
        graph_db.run("CREATE (n:数字 {name: '26'})")
        graph_db.run("CREATE (n:数字 {name: '8'})")
        graph_db.run("CREATE (n:数字 {name: '34'})")
        
        # 添加运算操作节点
        print("添加运算操作节点...")
        graph_db.run("CREATE (n:操作 {name: '加'})")
        graph_db.run("CREATE (n:运算 {name: '(*,26,加,8)'})")
        
        # 添加结果节点和关系
        print("添加结果关系...")
        graph_db.run("""
            MATCH (op:运算 {name: '(*,26,加,8)'}), (result:数字 {name: '34'})
            CREATE (op)-[:等于]->(result)
        """)
        
        # 添加更多运算知识
        print("添加详细运算知识...")
        
        # 创建运算过程节点
        cypher_queries = [
            "CREATE (n:运算过程 {name: '26加8', 步骤: '个位计算', 描述: '6+8=14,写4进1'})",
            "CREATE (n:运算过程 {name: '26加8', 步骤: '十位计算', 描述: '2+0+1=3'})",
            "CREATE (n:结果 {name: '34', 类型: '加法结果'})",
            
            # 建立关系
            """
            MATCH (calc:运算 {name: '(*,26,加,8)'}), (result:结果 {name: '34'})
            CREATE (calc)-[:得出]->(result)
            """,
            
            # 添加推理步骤
            """
            CREATE (step1:步骤 {name: '步骤1', 描述: '识别操作数26和8'})
            """,
            """
            CREATE (step2:步骤 {name: '步骤2', 描述: '执行加法运算'})
            """,
            """
            CREATE (step3:步骤 {name: '步骤3', 描述: '得出结果34'})
            """,
            
            # 连接步骤
            """
            MATCH (s1:步骤 {name: '步骤1'}), (s2:步骤 {name: '步骤2'}), (s3:步骤 {name: '步骤3'})
            CREATE (s1)-[:然后]->(s2)-[:然后]->(s3)
            """
        ]
        
        for query in cypher_queries:
            try:
                graph_db.run(query)
                print(f"执行查询成功: {query[:50]}...")
            except Exception as e:
                print(f"执行查询失败: {e}")
        
        # 验证数据
        print("\n验证添加的数据...")
        result = graph_db.run("MATCH (n) WHERE n.name CONTAINS '26' OR n.name CONTAINS '34' RETURN n.name as name, labels(n) as labels")
        
        for record in result:
            print(f"节点: {record['name']}, 标签: {record['labels']}")
        
        # 测试运算查询
        print("\n测试运算查询...")
        test_query = "MATCH (n{name:'(*,26,加,8)'})-[r]->(result) WHERE r.name='等于' RETURN result.name as answer"
        result = graph_db.run(test_query)
        
        for record in result:
            print(f"查询结果: {record['answer']}")
        
        print("运算知识设置完成!")
        return True
        
    except Exception as e:
        print(f"设置运算知识失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== 设置运算知识到Neo4j数据库 ===")
    success = setup_calculation_knowledge()
    
    if success:
        print("✓ 运算知识设置成功")
    else:
        print("✗ 运算知识设置失败")

if __name__ == "__main__":
    main()
